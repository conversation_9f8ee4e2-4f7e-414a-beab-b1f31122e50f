#!/usr/bin/env python3
"""
Skript zur Vorab-Generierung von Thumbnails für bessere Performance
"""

import os
import sys
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path

# Füge das Hauptverzeichnis zum Python-Pfad hinzu
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.media_service import MediaService
from config import Config

def generate_thumbnail_for_file(media_service, file_path):
    """Generiert ein Thumbnail für eine einzelne Datei"""
    try:
        start_time = time.time()
        thumbnail_path = media_service.get_or_create_thumbnail(file_path)
        duration = time.time() - start_time
        
        if thumbnail_path:
            print(f"✓ {os.path.basename(file_path)} -> {thumbnail_path} ({duration:.2f}s)")
            return True, duration
        else:
            print(f"✗ Fehler bei {os.path.basename(file_path)}")
            return False, duration
    except Exception as e:
        print(f"✗ Fehler bei {os.path.basename(file_path)}: {e}")
        return False, 0

def main():
    """Hauptfunktion für die Thumbnail-Generierung"""
    print("🚀 Starte Vorab-Generierung von Thumbnails...")
    
    # Konfiguration laden
    config = Config()
    media_service = MediaService(config.__dict__)
    
    # Alle Creator laden
    creators = media_service.get_all_creators()
    if not creators:
        print("❌ Keine Creator gefunden!")
        return
    
    print(f"📁 Gefunden: {len(creators)} Creator")
    
    # Alle Mediendateien sammeln
    all_media_files = []
    for creator in creators:
        all_media_files.extend(creator.all_images)
        all_media_files.extend(creator.all_videos)
    
    print(f"📸 Gefunden: {len(all_media_files)} Mediendateien")
    
    if not all_media_files:
        print("❌ Keine Mediendateien gefunden!")
        return
    
    # Prüfen, welche Thumbnails bereits existieren
    existing_thumbnails = 0
    files_to_process = []
    
    for file_path in all_media_files:
        if media_service._get_existing_thumbnail(file_path):
            existing_thumbnails += 1
        else:
            files_to_process.append(file_path)
    
    print(f"✅ Bereits vorhanden: {existing_thumbnails} Thumbnails")
    print(f"🔄 Zu erstellen: {len(files_to_process)} Thumbnails")
    
    if not files_to_process:
        print("🎉 Alle Thumbnails sind bereits vorhanden!")
        return
    
    # Thumbnails parallel generieren
    start_time = time.time()
    successful = 0
    failed = 0
    total_processing_time = 0
    
    # Verwende ThreadPoolExecutor für parallele Verarbeitung
    max_workers = min(4, os.cpu_count() or 1)  # Maximal 4 Threads
    print(f"⚡ Verwende {max_workers} parallele Threads")
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Alle Tasks starten
        future_to_file = {
            executor.submit(generate_thumbnail_for_file, media_service, file_path): file_path
            for file_path in files_to_process
        }
        
        # Ergebnisse sammeln
        for i, future in enumerate(as_completed(future_to_file), 1):
            file_path = future_to_file[future]
            try:
                success, duration = future.result()
                total_processing_time += duration
                
                if success:
                    successful += 1
                else:
                    failed += 1
                    
                # Fortschritt anzeigen
                if i % 10 == 0 or i == len(files_to_process):
                    progress = (i / len(files_to_process)) * 100
                    print(f"📊 Fortschritt: {i}/{len(files_to_process)} ({progress:.1f}%)")
                    
            except Exception as e:
                print(f"✗ Unerwarteter Fehler bei {os.path.basename(file_path)}: {e}")
                failed += 1
    
    # Statistiken
    total_time = time.time() - start_time
    avg_time_per_thumbnail = total_processing_time / len(files_to_process) if files_to_process else 0
    
    print("\n" + "="*50)
    print("📈 STATISTIKEN")
    print("="*50)
    print(f"✅ Erfolgreich erstellt: {successful}")
    print(f"❌ Fehlgeschlagen: {failed}")
    print(f"⏱️  Gesamtzeit: {total_time:.2f}s")
    print(f"⚡ Durchschnitt pro Thumbnail: {avg_time_per_thumbnail:.2f}s")
    print(f"🚀 Thumbnails/Sekunde: {len(files_to_process)/total_time:.2f}")
    
    if successful > 0:
        print(f"\n🎉 Thumbnail-Generierung abgeschlossen!")
        print(f"💡 Die mobile Galerie sollte jetzt deutlich schneller laden.")
    else:
        print(f"\n❌ Keine Thumbnails konnten erstellt werden.")

if __name__ == "__main__":
    main()
