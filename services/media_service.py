import os
import random
import time
import functools
from pathlib import Path
from PIL import Image, ImageOps
from models.creator import Creator
import hashlib
import cv2
import numpy as np
from cachetools import TTLCache
from cachetools.func import cached as cachetools_cached # Alias to avoid confusion if 'cached' is used as a variable name

# Define Cache Instances with different TTLs and maxsizes
thumbnail_cache = TTLCache(maxsize=1024, ttl=86400)  # For get_or_create_thumbnail (24 hours)
metadata_cache = TTLCache(maxsize=2048, ttl=86400)   # For _get_metadata_for_media (24 hours)
media_batch_cache = TTLCache(maxsize=128, ttl=300)    # For get_media_batch (5 minutes)

class MediaService:
    """
    Service für die Verarbeitung von medienbezogenen Operationen
    """
    def __init__(self, config):
        self.config = config
        self.creator_dir = config.get('CREATOR_DIR', '')
        self.allowed_image_extensions = config.get('ALLOWED_IMAGE_EXTENSIONS', set())
        self.allowed_video_extensions = config.get('ALLOWED_VIDEO_EXTENSIONS', set())
        self.meta_suffix = config.get('META_FILE_SUFFIX', '_meta.txt')
        self.thumbnail_dir = config.get('THUMBNAIL_DIR', os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'thumbnails'))
        self.thumbnail_size = config.get('THUMBNAIL_SIZE', (300, 300))
        self.thumbnail_quality = config.get('THUMBNAIL_QUALITY', 85)

    def get_all_creators(self):
        """
        Alle Creators aus dem Creator-Verzeichnis abrufen
        """
        print("\n=== GET_ALL_CREATORS ===")
        print(f"Suche im Verzeichnis: {self.creator_dir}")

        if not os.path.exists(self.creator_dir):
            print("FEHLER: Creator-Verzeichnis existiert nicht!")
            return []

        creators = []
        creator_dirs = [d for d in os.listdir(self.creator_dir)
                      if os.path.isdir(os.path.join(self.creator_dir, d))]

        print(f"Gefundene Verzeichnisse: {len(creator_dirs)}")

        for i, creator_name in enumerate(creator_dirs, 1):
            try:
                creator_path = os.path.join(self.creator_dir, creator_name)
                print(f"\nLade Creator {i}/{len(creator_dirs)}: {creator_name}")

                creator = Creator(
                    id=creator_name,
                    name=creator_name,
                    path=creator_path,
                    meta_suffix=self.meta_suffix,
                    allowed_image_extensions=self.allowed_image_extensions,
                    allowed_video_extensions=self.allowed_video_extensions
                )

                # Debug-Ausgabe der gefundenen Medien
                print(f"  Bilder: {len(creator.all_images)}")
                print(f"  Videos: {len(creator.all_videos)}")

                creators.append(creator)

            except Exception as e:
                print(f"FEHLER beim Verarbeiten von {creator_name}: {str(e)}")
                import traceback
                traceback.print_exc()

        print(f"\nGesamt geladene Creator: {len(creators)}")
        return creators

    def get_creator_by_id(self, creator_id):
        """
        Einen Creator anhand seiner ID abrufen
        """
        creator_path = os.path.join(self.creator_dir, creator_id)
        if os.path.exists(creator_path) and os.path.isdir(creator_path):
            return Creator(
                id=creator_id,
                name=creator_id,
                path=creator_path,
                meta_suffix=self.meta_suffix,
                allowed_image_extensions=self.allowed_image_extensions,
                allowed_video_extensions=self.allowed_video_extensions
            )
        return None

    def get_random_previews(self, creator, count=4):
        """
        Zufällige Vorschaubilder für einen Creator abrufen und Thumbnails erstellen
        """
        all_images = creator.all_images
        if not all_images:
            return []

        # Sicherstellen, dass wir nicht mehr Bilder anfordern, als verfügbar sind
        count = min(count, len(all_images))
        selected_images = random.sample(all_images, count)

        # Thumbnails für die ausgewählten Bilder erstellen
        thumbnails = []
        for image_path in selected_images:
            thumbnail_path = self.get_or_create_thumbnail(image_path)
            if thumbnail_path:
                thumbnails.append((image_path, thumbnail_path))

        return thumbnails

    def get_all_media_items(self):
        """
        Alle Medienelemente (Bilder und Videos) von allen Creators abrufen und Thumbnails erstellen
        Gibt eine Liste von Dictionaries zurück (path, thumbnail_path, type, creator_id, creator_name, metadata)
        """
        media_items = []
        creators = self.get_all_creators()

        for creator in creators:
            # Bilder hinzufügen
            for image_path in creator.all_images:
                thumbnail_path = self.get_or_create_thumbnail(image_path)
                metadata = self._get_metadata_for_media(image_path, creator)
                media_items.append({
                    'path': image_path,
                    'thumbnail_path': thumbnail_path,
                    'type': 'image',
                    'creator_id': creator.id,
                    'creator_name': creator.name,
                    'metadata': metadata
                })

            # Videos hinzufügen
            for video_path in creator.all_videos:
                thumbnail_path = self.get_or_create_thumbnail(video_path)
                metadata = self._get_metadata_for_media(video_path, creator)
                media_items.append({
                    'path': video_path,
                    'thumbnail_path': thumbnail_path,
                    'type': 'video',
                    'creator_id': creator.id,
                    'creator_name': creator.name,
                    'metadata': metadata
                })

        # Die Elemente zufällig mischen
        random.shuffle(media_items)
        return media_items

    @cachetools_cached(cache=media_batch_cache)
    def get_media_batch(self, offset=0, limit=5, creator_filter=None, fast_mode=False):
        """
        Lädt einen Batch von Medienelementen mit Paginierung - Performance-optimiert

        Args:
            offset: Startposition für die Paginierung
            limit: Maximale Anzahl der zurückzugebenden Elemente (Standard: 5 für bessere Performance)
            creator_filter: Optionaler Filter für bestimmte Creator
            fast_mode: Wenn True, werden Thumbnails asynchron erstellt

        Returns:
            Dictionary mit den Medien und Metadaten
        """
        try:
            creators_to_process = []
            if creator_filter:
                creator = self.get_creator_by_id(creator_filter)
                if creator:
                    creators_to_process.append(creator)
            else:
                creators_to_process = self.get_all_creators()

            if not creators_to_process and creator_filter:
                 return {
                    'success': True, # Success, but no items for this specific creator
                    'data': {
                        'items': [],
                        'total': 0,
                        'offset': offset,
                        'limit': limit,
                        'has_more': False
                    }
                }
            elif not creators_to_process:
                return {
                    'success': False,
                    'error': 'Keine Creator gefunden oder Creator-Verzeichnis existiert nicht.'
                }

            all_media_items_for_batch = []
            for creator in creators_to_process:
                for image_path in creator.all_images:
                    try:
                        all_media_items_for_batch.append({
                            'path': image_path,
                            'type': 'image',
                            'creator_id': creator.id,
                            'creator_name': creator.name,
                            'filename': os.path.basename(image_path),
                            'mtime': os.path.getmtime(image_path)
                        })
                    except FileNotFoundError:
                        print(f"Datei nicht gefunden (wird übersprungen): {image_path}")

                for video_path in creator.all_videos:
                    try:
                        all_media_items_for_batch.append({
                            'path': video_path,
                            'type': 'video',
                            'creator_id': creator.id,
                            'creator_name': creator.name,
                            'filename': os.path.basename(video_path),
                            'mtime': os.path.getmtime(video_path)
                        })
                    except FileNotFoundError:
                        print(f"Datei nicht gefunden (wird übersprungen): {video_path}")

            # Sort all collected media items by modification time (newest first)
            all_media_items_for_batch.sort(key=lambda x: x['mtime'], reverse=True)

            total_actual_items = len(all_media_items_for_batch)

            # Apply pagination to the sorted list
            start_index = offset
            end_index = offset + limit
            paginated_media_infos = all_media_items_for_batch[start_index:end_index]

            result_items = []
            for media_info in paginated_media_infos:
                try:
                    # Performance-Optimierung: Thumbnail-Erstellung je nach Modus
                    if fast_mode:
                        # Im Fast-Mode: Prüfe nur, ob Thumbnail existiert, erstelle es nicht
                        thumbnail_path = self._get_existing_thumbnail(media_info['path'])
                        if not thumbnail_path:
                            # Verwende einen Platzhalter oder erstelle asynchron
                            thumbnail_path = None
                    else:
                        # Normal-Mode: Erstelle Thumbnail synchron (langsamer)
                        thumbnail_path = self.get_or_create_thumbnail(media_info['path'])

                    # Metadaten laden (gecacht)
                    metadata = self._get_metadata_for_media(media_info['path'], media_info['creator_name'])

                    # Konvertiere absolute Pfade in relative Pfade für die API-Antwort
                    # Basisverzeichnis ist das 'public' Verzeichnis der Anwendung
                    public_base_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'public')
                    try:
                        rel_path = os.path.relpath(media_info['path'], public_base_dir)
                    except ValueError: # Wenn der Pfad nicht relativ zu public_base_dir ist (z.B. anderer Laufwerksbuchstabe unter Windows)
                        rel_path = media_info['path'] # Fallback auf den ursprünglichen Pfad

                    result_items.append({
                        'path': rel_path.replace('\\', '/'),  # Windows-Kompatibilität
                        'thumbnail_path': thumbnail_path.replace('\\', '/') if thumbnail_path else None,
                        'type': media_info['type'],
                        'creator_id': media_info['creator_id'],
                        'creator_name': media_info['creator_name'],
                        'metadata': metadata or {}
                    })
                except Exception as e:
                    print(f"Fehler beim Verarbeiten von {media_info.get('path', 'unbekannt')} für Batch: {str(e)}")
                    # Im Fast-Mode: Fehler nicht abbrechen lassen
                    if not fast_mode:
                        continue

            has_more = (offset + len(result_items)) < total_actual_items

            return {
                'success': True,
                'data': {
                    'items': result_items,
                    'total': total_actual_items,
                    'offset': offset,
                    'limit': limit,
                    'has_more': has_more
                }
            }

        except Exception as e:
            error_msg = f"Fehler in get_media_batch: {str(e)}"
            print(f"\n!!! FEHLER: {error_msg}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': 'Ein interner Fehler ist aufgetreten beim Laden der Medien.'
            }

    @cachetools_cached(cache=metadata_cache)
    def _get_metadata_for_media(self, media_path, creator_name):
        """
        Holt Metadaten für eine Mediendatei

        Args:
            media_path: Pfad zur Mediendatei
            creator_name: Name des Creators (für den Cache-Key)

        Returns:
            Dictionary mit den Metadaten oder leeres Dictionary bei Fehler
        """
        # Cache-Key erstellen (basierend auf Dateiname und Änderungsdatum)
        try:
            mtime = os.path.getmtime(media_path)
            cache_key = f"metadata_{creator_name}_{os.path.basename(media_path)}_{int(mtime)}"

            # Prüfen, ob die Metadaten im Cache sind
            cached_meta = metadata_cache.get(cache_key)
            if cached_meta is not None:
                return cached_meta

            # Metadaten-Datei suchen
            meta_path = f"{media_path}{self.meta_suffix}"
            if not os.path.exists(meta_path):
                return {}

            with open(meta_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    return {}

                # Versuchen, den Inhalt als JSON zu parsen
                try:
                    import json
                    metadata = json.loads(content)
                    # Im Cache speichern
                    metadata_cache[cache_key] = metadata
                    return metadata

                except json.JSONDecodeError:
                    # Falls kein JSON, als Key-Value-Paare parsen
                    metadata = {}
                    prompt_lines = []

                    for line in content.split('\n'):
                        line = line.strip()
                        if not line:
                            continue

                        if ':' in line:
                            key, value = line.split(':', 1)
                            key = key.strip()
                            value = value.strip()

                            # Spezielle Behandlung für Prompts
                            if 'prompt' in key.lower() and not key.lower().startswith('negative'):
                                prompt_lines.append(value)
                            else:
                                metadata[key] = value

                    # Verarbeite den gesamten Prompt
                    if prompt_lines:
                        full_prompt = ' '.join(prompt_lines)
                        metadata['prompt'] = full_prompt

                    # Im Cache speichern
                    metadata_cache[cache_key] = metadata
                    return metadata

        except Exception as e:
            print(f"Fehler beim Lesen der Metadaten aus {media_path}: {str(e)}")
            return {}

    def get_media_metadata(self, media_path):
        """
        Metadaten für ein bestimmtes Medium abrufen
        """
        meta_path = f"{media_path}{self.meta_suffix}"

        if os.path.exists(meta_path):
            try:
                with open(meta_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    metadata = {}
                    for line in content.split('\n'):
                        if ':' in line:
                            key, value = line.split(':', 1)
                            metadata[key.strip()] = value.strip()
                    return metadata
            except Exception as e:
                print(f"Fehler beim Laden der Metadaten für {media_path}: {e}")
        return {}

    def _get_existing_thumbnail(self, media_path):
        """
        Prüft, ob ein Thumbnail bereits existiert, ohne es zu erstellen

        Args:
            media_path: Pfad zur Mediendatei

        Returns:
            Relativer Pfad zum Thumbnail oder None, wenn es nicht existiert
        """
        try:
            # Konvertiere relativen Pfad in absoluten Pfad, falls nötig
            if not os.path.isabs(media_path):
                base_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)))
                media_path = os.path.abspath(os.path.join(base_dir, 'public', media_path.lstrip('/')))

            # Erstelle einen eindeutigen Dateinamen für das Thumbnail
            import hashlib
            media_hash = hashlib.md5(media_path.encode('utf-8')).hexdigest()
            thumbnail_filename = f"{media_hash}_{self.thumbnail_size[0]}x{self.thumbnail_size[1]}.jpg"
            thumbnail_path = os.path.join(self.thumbnail_dir, thumbnail_filename)

            # Prüfen, ob das Thumbnail bereits existiert
            if os.path.exists(thumbnail_path):
                return f"thumbnails/{thumbnail_filename}"

            return None

        except Exception as e:
            print(f"Fehler beim Prüfen des Thumbnails für {media_path}: {e}")
            return None

    @cachetools_cached(cache=thumbnail_cache)
    def get_or_create_thumbnail(self, media_path):
        """
        Erstellt ein Thumbnail für ein Medium (Bild oder Video), falls es noch nicht existiert

        Args:
            media_path: Pfad zur Mediendatei (kann absolut oder relativ sein)

        Returns:
            Relativer Pfad zum Thumbnail (z.B. 'thumbnails/filename_300x300.jpg')
        """
        try:
            # Konvertiere relativen Pfad in absoluten Pfad, falls nötig
            if not os.path.isabs(media_path):
                base_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)))
                media_path = os.path.abspath(os.path.join(base_dir, 'public', media_path.lstrip('/')))

            # Prüfen, ob die Mediendatei existiert
            if not os.path.exists(media_path):
                print(f"FEHLER: Mediendatei nicht gefunden: {media_path}")
                return None

            # Prüfen, ob es sich um ein Bild oder Video handelt
            extension = Path(media_path).suffix.lower()[1:]

            # Stelle sicher, dass das Thumbnail-Verzeichnis existiert
            os.makedirs(self.thumbnail_dir, exist_ok=True)

            # Erstelle einen eindeutigen Dateinamen für das Thumbnail
            import hashlib
            media_hash = hashlib.md5(media_path.encode('utf-8')).hexdigest()
            thumbnail_filename = f"{media_hash}_{self.thumbnail_size[0]}x{self.thumbnail_size[1]}.jpg"
            thumbnail_path = os.path.join(self.thumbnail_dir, thumbnail_filename)

            # Prüfen, ob das Thumbnail bereits existiert
            if os.path.exists(thumbnail_path):
                return f"thumbnails/{thumbnail_filename}"

            if extension in self.allowed_image_extensions:
                # Für Bilder - Optimiert für Performance
                try:
                    img = Image.open(media_path)
                    # Apply EXIF orientation correction
                    img = ImageOps.exif_transpose(img)

                    # Konvertiere zu RGB, falls nötig
                    if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                        if img.mode == 'RGBA':
                            background = Image.new('RGB', img.size, (255, 255, 255))
                            background.paste(img, mask=img.split()[3])  # 3 ist der Alpha-Kanal
                            img = background
                        elif img.mode == 'LA':
                            background = Image.new('L', img.size, 255)
                            background.paste(img, mask=img.split()[1])  # 1 ist der Alpha-Kanal
                            img = Image.merge('RGB', (background, background, background))
                        elif img.mode == 'P':
                            img = img.convert('RGBA')
                            background = Image.new('RGB', img.size, (255, 255, 255))
                            background.paste(img, mask=img.split()[3])
                            img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')

                    # Thumbnail erstellen
                    img.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)

                    # Speichern mit optimierten Optionen
                    img.save(thumbnail_path, 'JPEG',
                            quality=self.thumbnail_quality,
                            optimize=True,
                            progressive=True)

                    return f"thumbnails/{thumbnail_filename}"

                except Exception as e:
                    print(f"FEHLER beim Erstellen des Thumbnails für {media_path}: {e}")
                    return None

            elif extension in self.allowed_video_extensions:
                # Für Videos - Optimiert für Performance
                try:
                    import cv2

                    # Öffne das Video
                    cap = cv2.VideoCapture(media_path)
                    if not cap.isOpened():
                        print(f"FEHLER: Video konnte nicht geöffnet werden: {media_path}")
                        return None

                    # Versuche, einen Frame aus der Mitte des Videos zu bekommen
                    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    target_frame = max(0, total_frames // 2)
                    cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame)

                    success, frame = cap.read()
                    cap.release()

                    if not success:
                        print(f"FEHLER: Konnte keinen Frame aus dem Video lesen: {media_path}")
                        return None

                    # Konvertiere von BGR zu RGB
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                    # Erstelle ein PIL-Bild aus dem Frame
                    img = Image.fromarray(frame_rgb)

                    # Erstelle das Thumbnail
                    img.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)

                    # Speichere das Thumbnail
                    img.save(thumbnail_path, 'JPEG',
                           quality=self.thumbnail_quality,
                           optimize=True,
                           progressive=True)

                    return f"thumbnails/{thumbnail_filename}"

                except Exception as e:
                    print(f"FEHLER beim Erstellen des Video-Thumbnails für {media_path}: {e}")
                    return None

            print(f"Nicht unterstützter Dateityp: {extension}")
            return None

        except Exception as e:
            print(f"Unerwarteter Fehler in get_or_create_thumbnail für {media_path}: {e}")
            import traceback
            traceback.print_exc()
            return None

    def get_relative_path(self, absolute_path):
        """
        Einen absoluten Pfad in einen relativen Pfad für die Verwendung in Templates umwandeln
        """
        # Da wir nicht mehr auf current_app.root_path zugreifen können,
        # verwenden wir eine alternative Methode, um relative Pfade zu erzeugen
        if not absolute_path:
            return ""

        if isinstance(absolute_path, str) and '/public/' in absolute_path:
            # Wenn der Pfad '/public/' enthält, extrahieren wir den Teil nach '/public/'
            parts = absolute_path.split('/public/')
            if len(parts) > 1:
                return 'public/' + parts[1]
        return absolute_path
