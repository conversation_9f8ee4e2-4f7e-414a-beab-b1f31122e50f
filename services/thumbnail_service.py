"""
Background Thumbnail Service für asynchrone Thumbnail-Generierung
"""

import os
import threading
import queue
import time
from concurrent.futures import ThreadPoolExecutor
from services.media_service import MediaService

class ThumbnailService:
    """
    Service für die asynchrone Generierung von Thumbnails im Hintergrund
    """

    def __init__(self, config, max_workers=2):
        self.config = config
        self.media_service = MediaService(config)
        self.max_workers = max_workers
        self.thumbnail_queue = queue.Queue()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.running = False
        self.stats = {
            'processed': 0,
            'failed': 0,
            'queue_size': 0
        }

    def start(self):
        """Startet den Background-Service"""
        if self.running:
            return

        self.running = True
        print(f"🚀 Thumbnail-Service gestartet mit {self.max_workers} Threads")

        # Starte Worker-Thread
        self.worker_thread = threading.Thread(target=self._worker, daemon=True)
        self.worker_thread.start()

    def stop(self):
        """Stoppt den Background-Service"""
        self.running = False
        self.executor.shutdown(wait=True)
        print("🛑 Thumbnail-Service gestoppt")

    def queue_thumbnail(self, media_path, priority=1):
        """
        Fügt eine Thumbnail-Generierung zur Warteschlange hinzu

        Args:
            media_path: Pfad zur Mediendatei
            priority: Priorität (1=hoch, 2=normal, 3=niedrig)
        """
        if not self.running:
            self.start()

        # Prüfe, ob Thumbnail bereits existiert
        if self.media_service._get_existing_thumbnail(media_path):
            return  # Bereits vorhanden

        self.thumbnail_queue.put((priority, time.time(), media_path))
        self.stats['queue_size'] = self.thumbnail_queue.qsize()

    def _worker(self):
        """Worker-Thread für die Thumbnail-Generierung"""
        while self.running:
            try:
                # Hole nächste Aufgabe aus der Queue (mit Timeout)
                try:
                    priority, timestamp, media_path = self.thumbnail_queue.get(timeout=1.0)
                except queue.Empty:
                    continue

                # Generiere Thumbnail
                future = self.executor.submit(self._generate_thumbnail, media_path)

                # Warte auf Ergebnis (non-blocking)
                try:
                    success = future.result(timeout=30.0)  # 30 Sekunden Timeout
                    if success:
                        self.stats['processed'] += 1
                    else:
                        self.stats['failed'] += 1
                except Exception as e:
                    print(f"❌ Thumbnail-Generierung fehlgeschlagen für {media_path}: {e}")
                    self.stats['failed'] += 1

                self.thumbnail_queue.task_done()
                self.stats['queue_size'] = self.thumbnail_queue.qsize()

            except Exception as e:
                print(f"❌ Fehler im Thumbnail-Worker: {e}")
                time.sleep(1)

    def _generate_thumbnail(self, media_path):
        """Generiert ein Thumbnail für eine Mediendatei"""
        try:
            start_time = time.time()
            thumbnail_path = self.media_service.get_or_create_thumbnail(media_path)
            duration = time.time() - start_time

            if thumbnail_path:
                print(f"✓ Thumbnail erstellt: {os.path.basename(media_path)} ({duration:.2f}s)")
                return True
            else:
                print(f"❌ Thumbnail-Erstellung fehlgeschlagen: {os.path.basename(media_path)}")
                return False

        except Exception as e:
            print(f"❌ Fehler bei Thumbnail-Erstellung für {media_path}: {e}")
            return False

    def get_stats(self):
        """Gibt aktuelle Statistiken zurück"""
        return {
            'processed': self.stats['processed'],
            'failed': self.stats['failed'],
            'queue_size': self.stats['queue_size'],
            'running': self.running
        }

    def preload_thumbnails_for_batch(self, media_items):
        """
        Lädt Thumbnails für einen Batch von Medien vor

        Args:
            media_items: Liste von Media-Items
        """
        for item in media_items:
            if 'path' in item:
                self.queue_thumbnail(item['path'], priority=2)  # Normale Priorität

    def preload_thumbnails_for_creator(self, creator):
        """
        Lädt alle Thumbnails für einen Creator vor

        Args:
            creator: Creator-Objekt
        """
        # Bilder mit hoher Priorität
        for image_path in creator.all_images[:10]:  # Erste 10 Bilder
            self.queue_thumbnail(image_path, priority=1)

        # Restliche Bilder mit normaler Priorität
        for image_path in creator.all_images[10:]:
            self.queue_thumbnail(image_path, priority=2)

        # Videos mit niedriger Priorität (dauern länger)
        for video_path in creator.all_videos:
            self.queue_thumbnail(video_path, priority=3)

# Globale Instanz
_thumbnail_service = None

def get_thumbnail_service(config=None):
    """Gibt die globale Thumbnail-Service-Instanz zurück"""
    global _thumbnail_service
    if _thumbnail_service is None and config:
        _thumbnail_service = ThumbnailService(config)
    return _thumbnail_service

def init_thumbnail_service(config):
    """Initialisiert den globalen Thumbnail-Service"""
    global _thumbnail_service
    if _thumbnail_service is None:
        # Konvertiere Flask-Config zu Dictionary falls nötig
        if hasattr(config, '__dict__'):
            config_dict = config.__dict__
        elif hasattr(config, 'get'):
            # Flask Config object
            config_dict = {key: config[key] for key in config.keys()}
        else:
            config_dict = config

        _thumbnail_service = ThumbnailService(config_dict)
        _thumbnail_service.start()
    return _thumbnail_service
