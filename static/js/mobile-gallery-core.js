// Wrap in IIFE to prevent global scope pollution
(function() {
// Core variables - Make these globally available
window.currentIndex = 0;
window.isLoading = false;
window.totalLoaded = 0;
window.totalItems = 0;
window.PRELOAD_BUFFER = 2;
window.SWIPE_THRESHOLD = 50;
window.IMAGE_LOAD_TIMEOUT = 3000;

// Initialize gallery - Core initialization only
function initGallery() {
    console.log('Initializing gallery core...');

    // Setze die Gesamtanzahl der Elemente, falls verfügbar
    const container = document.getElementById('swipeContainer');
    if (container) {
        const total = parseInt(container.getAttribute('data-total'), 10) || 0;
        if (total > 0) {
            window.totalItems = total;
            console.log('Total items from data attribute:', window.totalItems);
        }
    }

    // Initialisiere Lazy Loading
    setupIntersectionObserver();

    // Lade das erste Medium
    const firstMedia = document.querySelector('.swipe-item[data-index="0"] .media-container img, .swipe-item[data-index="0"] .media-container video');
    if (firstMedia?.dataset.src) {
        console.log('Loading first media element');
        if (typeof window.loadMediaElement === 'function') {
            window.loadMediaElement(firstMedia, firstMedia.dataset.src);
        }
    }

    console.log('Gallery core initialization complete');
}

// Setup IntersectionObserver for TikTok-style lazy loading and video autoplay
function setupIntersectionObserver() {
    if (!('IntersectionObserver' in window)) {
        initLazyLoading();
        return;
    }

    // Observer for images
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                const fullSrc = img.getAttribute('data-full');
                if (fullSrc && img.src !== fullSrc) {
                    img.src = fullSrc;
                }
                imageObserver.unobserve(img);
            }
        });
    }, {
        rootMargin: '200px 0px',
        threshold: 0.01
    });

    // Observer for TikTok-style video autoplay
    const videoObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const video = entry.target.querySelector('video');
            if (!video) return;

            if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                // Auto-play when video is mostly visible
                video.currentTime = 0;
                video.play().catch(e => {
                    console.log('Video autoplay prevented:', e);
                    // Show play button if autoplay fails
                    const playButton = entry.target.querySelector('.play-button');
                    if (playButton) playButton.style.display = 'flex';
                });
            } else {
                // Pause when video is not visible
                video.pause();
            }
        });
    }, {
        threshold: 0.5,
        rootMargin: '0px'
    });

    // Observe existing images
    document.querySelectorAll('img.lazy-load-image').forEach(img => {
        const fullSrc = img.getAttribute('data-full');
        if (fullSrc && img.src !== fullSrc) {
            imageObserver.observe(img);
        }
    });

    // Observe existing video containers
    document.querySelectorAll('.swipe-item').forEach(item => {
        const video = item.querySelector('video');
        if (video) {
            videoObserver.observe(item);
        }
    });

    // Store observers globally for new items
    window.imageObserver = imageObserver;
    window.videoObserver = videoObserver;
}

// Fallback lazy loading for browsers without IntersectionObserver
function initLazyLoading() {
    function loadVisibleMedia() {
        const images = document.querySelectorAll('img.lazy-load-image');
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

        images.forEach(img => {
            const imgTop = img.getBoundingClientRect().top + scrollTop;
            const imgBottom = imgTop + img.offsetHeight;

            if (imgBottom > scrollTop && imgTop < (scrollTop + viewportHeight)) {
                const fullSrc = img.getAttribute('data-full');
                if (fullSrc && img.src !== fullSrc) {
                    img.src = fullSrc;
                }
            }
        });
    }

    loadVisibleMedia();

    let isThrottled = false;
    function throttle(callback, limit) {
        if (!isThrottled) {
            callback();
            isThrottled = true;
            setTimeout(() => {
                isThrottled = false;
            }, limit);
        }
    }

    window.addEventListener('scroll', () => {
        throttle(loadVisibleMedia, 100);
    });

    window.addEventListener('resize', () => {
        throttle(loadVisibleMedia, 100);
    });

    document.querySelectorAll('img').forEach(img => {
        img.onerror = function() {
            const fullSrc = this.getAttribute('data-full');
            if (fullSrc && this.src !== fullSrc) {
                this.src = fullSrc;
            }
        };
    });
}

// Load full resolution image
function loadFullImage(imgElement) {
    if (!imgElement || !(imgElement instanceof HTMLElement)) {
        console.warn('loadFullImage: Invalid image element provided.');
        return;
    }
    const fullSizeUrl = imgElement.dataset.full;
    if (!fullSizeUrl) {
        console.warn('loadFullImage: No data-full attribute found on image:', imgElement);
        return;
    }
    // Prevent reloading if already loaded or currently loading to full source
    if (imgElement.src === fullSizeUrl || imgElement.dataset.loadingFull === 'true') {
        return;
    }
    imgElement.dataset.loadingFull = 'true'; // Mark as loading full image
    console.log('Loading full image for:', fullSizeUrl);
    imgElement.src = fullSizeUrl;
    imgElement.onload = () => {
        delete imgElement.dataset.loadingFull;
    };
    imgElement.onerror = () => {
        console.error('Error loading full image:', fullSizeUrl);
        delete imgElement.dataset.loadingFull;
        // Optionally, revert to thumbnail or show a placeholder
    };
}

// Initialize the gallery when the DOM is loaded
console.log('Adding DOMContentLoaded event listener...');
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOMContentLoaded event fired, initializing gallery...');
    try {
        initGallery();
        console.log('Gallery initialization completed');

        // Initial media loading will be triggered by mobile-gallery-media.js
        // if (typeof window.loadMoreMedia === 'function') {
        //     console.log('Loading initial media batch...');
        //     window.loadMoreMedia(); // Load default number of items
        // } else {
        //     console.error('loadMoreMedia function not found. Initial media will not be loaded.');
        // }
    } catch (error) {
        console.error('Error initializing gallery:', error);
    }
});

// Make functions available globally
window.initGallery = initGallery;
window.loadFullImage = loadFullImage;
window.setupIntersectionObserver = setupIntersectionObserver;
window.initLazyLoading = initLazyLoading;

console.log('Core module initialized and functions exposed');

})(); // End of IIFE