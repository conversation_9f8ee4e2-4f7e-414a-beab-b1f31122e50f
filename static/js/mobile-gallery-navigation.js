// Navigation and Swipe Functionality

// TikTok-style Navigation functions
function showItem(index) {
    console.group('showItem');
    console.log('TikTok-style showItem called with index:', index);

    const container = document.getElementById('swipeContainer');
    if (!container) {
        console.error('Container nicht gefunden');
        console.groupEnd();
        return;
    }

    const items = document.querySelectorAll('.swipe-item');
    const totalItems = parseInt(container.getAttribute('data-total') || '0');
    const loadedItems = items.length;

    console.log('Aktueller Index:', index, 'Geladene Items:', loadedItems, 'Gesamtanzahl:', totalItems);

    // Wenn der Index außerhalb des gültigen Bereichs liegt, breche ab
    if (index < 0 || (index >= totalItems && totalItems > 0)) {
        console.warn(`Ungültiger Index: ${index}, Gesamtanzahl: ${totalItems}`);
        console.groupEnd();
        return;
    }

    // Aktuellen Index aktualisieren
    window.currentIndex = index;
    console.log('Neuer aktueller Index:', window.currentIndex);

    // Prüfen, ob wir mehr Medien laden müssen (wenn wir uns dem Ende nähern)
    if (index >= loadedItems - 3 && loadedItems < totalItems && typeof window.checkLoadMore === 'function') {
        console.log('Prüfe ob mehr Medien geladen werden müssen...');
        window.checkLoadMore(loadedItems);
    }

    // Alle Items durchgehen und TikTok-style positioning
    items.forEach((item, i) => {
        const itemIndex = parseInt(item.getAttribute('data-index') || i);

        // Remove all positioning classes first
        item.classList.remove('active', 'prev', 'next');

        if (itemIndex === index) {
            // Aktuelles Item anzeigen
            item.style.display = 'flex';
            item.classList.add('active');
            console.log('Item made visible in DOM:', item);

            // Auto-play videos when they become active
            const video = item.querySelector('video');
            if (video) {
                // Pause all other videos first
                document.querySelectorAll('video').forEach(v => {
                    if (v !== video) v.pause();
                });

                // Try to autoplay the current video
                video.currentTime = 0;
                video.play().catch(e => {
                    console.log('Autoplay prevented:', e);
                    // Show play button if autoplay fails
                    const playButton = item.querySelector('.play-button');
                    if (playButton) playButton.style.display = 'flex';
                });
            }

            // Lazy Loading für das aktuelle Bild
            const img = item.querySelector('img.lazy-load-image');
            if (img && typeof window.loadFullImage === 'function') {
                window.loadFullImage(img);
            }

        } else if (itemIndex === index - 1) {
            // Vorheriges Item
            item.style.display = 'flex';
            item.classList.add('prev');
        } else if (itemIndex === index + 1) {
            // Nächstes Item
            item.style.display = 'flex';
            item.classList.add('next');
        } else {
            // Alle anderen Items ausblenden
            item.style.display = 'none';

            // Pause videos that are not visible
            const video = item.querySelector('video');
            if (video) video.pause();
        }
    });

    // Update progress indicator
    if (typeof window.updateProgressIndicator === 'function') {
        window.updateProgressIndicator();
    }

    console.groupEnd();
}

function nextItem() {
    const items = document.querySelectorAll('.swipe-item');
    const totalItemsInDOM = items.length;
    const totalReportedItems = parseInt(document.getElementById('swipeContainer')?.getAttribute('data-total') || '0', 10);

    if (currentIndex < totalItemsInDOM - 1) {
        showItem(currentIndex + 1);
    } else if (totalItemsInDOM < totalReportedItems && typeof window.checkLoadMore === 'function') {
        // Wenn wir am Ende der geladenen Items sind, aber es insgesamt mehr gibt
        console.log('Am Ende der geladenen Items, versuche mehr zu laden...');
        window.checkLoadMore(totalItemsInDOM); // Fordere an, ab dem aktuellen Ende der DOM-Elemente zu laden
    } else {
        console.log('Ende der Galerie erreicht oder Nachladen nicht möglich.');
    }
}

function prevItem() {
    if (currentIndex > 0) {
        showItem(currentIndex - 1);
    } else {
        // Optional: Zum letzten Bild springen, wenn wir am Anfang sind
        // const items = document.querySelectorAll('.swipe-item');
        // if (items.length > 0) {
        //     showItem(items.length - 1);
        // }

        // Oder einfach nichts tun, wenn wir am ersten Bild sind
        console.log('Erstes Bild erreicht');
    }
}

// TikTok-style Swipe handling
let startY = null;
let startX = null;
let isScrolling = null;
let isDragging = false;
let currentTranslateY = 0;

function initSwipeListeners() {
    const container = document.querySelector('.swipe-container');
    if (!container) return;

    console.log('Initializing TikTok-style swipe listeners...');

    // Touch events for mobile
    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd, { passive: false });

    // Mouse events for desktop testing
    container.addEventListener('mousedown', handleMouseStart, { passive: false });
    container.addEventListener('mousemove', handleMouseMove, { passive: false });
    container.addEventListener('mouseup', handleMouseEnd, { passive: false });
    container.addEventListener('mouseleave', handleMouseEnd, { passive: false });

    // Keyboard navigation
    document.addEventListener('keydown', handleKeyDown);

    console.log('TikTok-style swipe listeners initialized');
}

function handleTouchStart(e) {
    // Don't interfere with UI elements
    if (e.target.closest('.tiktok-ui')) return;

    const touch = e.touches[0];
    startY = touch.clientY;
    startX = touch.clientX;
    isScrolling = undefined;
    isDragging = false;

    // Verhindere Standardverhalten, um Scrollen zu verhindern
    e.preventDefault();
    return false;
}

// Mouse event handlers for desktop testing
function handleMouseStart(e) {
    if (e.target.closest('.tiktok-ui')) return;

    startY = e.clientY;
    startX = e.clientX;
    isScrolling = undefined;
    isDragging = false;

    e.preventDefault();
}

function handleMouseMove(e) {
    if (startY === null || startX === null) return;
    if (e.target.closest('.tiktok-ui')) return;

    const yDiff = e.clientY - startY;
    const xDiff = e.clientX - startX;

    if (isScrolling === undefined) {
        isScrolling = Math.abs(xDiff) < Math.abs(yDiff);
    }

    if (isScrolling) {
        e.preventDefault();
        isDragging = true;

        const container = document.querySelector('.swipe-container');
        const currentItem = container ? container.querySelector('.swipe-item.active') : null;

        if (currentItem) {
            const dampening = 0.7;
            const translateY = yDiff * dampening;
            const opacity = Math.max(0.7, 1 - Math.abs(yDiff) / 300);

            currentItem.style.transition = 'none';
            currentItem.style.transform = `translateY(${translateY}px)`;
            currentItem.style.opacity = opacity;

            const nextItem = yDiff < 0 ? getNextItem() : getPrevItem();
            if (nextItem) {
                const direction = yDiff < 0 ? 1 : -1;
                nextItem.style.display = 'flex';
                nextItem.style.transition = 'none';
                nextItem.style.transform = `translateY(${100 * direction}%)`;
                nextItem.style.opacity = '0.7';
            }
        }
    }
}

function handleMouseEnd(e) {
    if (startY === null || startX === null) {
        resetTouch();
        return;
    }

    const yDiff = e.clientY - startY;
    const container = document.querySelector('.swipe-container');
    const currentItem = container ? container.querySelector('.swipe-item.active') : null;

    if (currentItem) {
        currentItem.style.transition = 'transform 0.3s ease-in-out, opacity 0.3s ease-in-out';
    }

    if (isScrolling && isDragging) {
        const SWIPE_THRESHOLD = 50;

        if (Math.abs(yDiff) > SWIPE_THRESHOLD) {
            if (yDiff < 0) {
                nextItem();
            } else {
                prevItem();
            }
        } else {
            resetItemsPosition();
        }
    }

    resetTouch();
}

// Keyboard navigation
function handleKeyDown(e) {
    switch(e.key) {
        case 'ArrowUp':
            e.preventDefault();
            prevItem();
            break;
        case 'ArrowDown':
            e.preventDefault();
            nextItem();
            break;
        case ' ':
            e.preventDefault();
            const currentVideo = document.querySelector('.swipe-item.active video');
            if (currentVideo) {
                if (currentVideo.paused) {
                    currentVideo.play();
                } else {
                    currentVideo.pause();
                }
            }
            break;
    }
}

function handleTouchMove(e) {
    if (startY === null || startX === null) return;

    // Don't interfere with UI elements
    if (e.target.closest('.tiktok-ui')) return;

    const touch = e.touches[0];
    const yDiff = touch.clientY - startY;
    const xDiff = touch.clientX - startX;

    // Bestimme die Hauptrichtung des Swipes
    if (isScrolling === undefined) {
        isScrolling = Math.abs(xDiff) < Math.abs(yDiff);
    }

    // Wenn es sich um eine vertikale Bewegung handelt
    if (isScrolling) {
        // Verhindere das Scrollen der Seite
        e.preventDefault();
        isDragging = true;

        // Berechne die Verschiebung basierend auf der vertikalen Bewegung
        const container = document.querySelector('.swipe-container');
        const currentItem = container ? container.querySelector('.swipe-item.active') : null;

        if (currentItem) {
            // Glättung der Bewegung mit einer leichten Dämpfung
            const dampening = 0.7;
            const translateY = yDiff * dampening;
            const opacity = Math.max(0.7, 1 - Math.abs(yDiff) / 300);

            // Aktualisiere die Position des aktuellen Elements
            currentItem.style.transition = 'none';
            currentItem.style.transform = `translateY(${translateY}px)`;
            currentItem.style.opacity = opacity;

            // Zeige das nächste oder vorherige Item an
            const nextItem = yDiff < 0 ? getNextItem() : getPrevItem();
            if (nextItem) {
                const direction = yDiff < 0 ? 1 : -1;
                nextItem.style.display = 'flex';
                nextItem.style.transition = 'none';
                nextItem.style.transform = `translateY(${100 * direction}%)`;
                nextItem.style.opacity = '0.7';
            }
        }
    }
}

function handleTouchEnd(e) {
    if (startY === null || startX === null) {
        resetTouch();
        return;
    }

    const touch = e.changedTouches[0];
    const yDiff = touch.clientY - startY;
    const container = document.querySelector('.swipe-container');
    const currentItem = container ? container.querySelector('.swipe-item.active') : null;

    // Setze die Transition für flüssige Animationen
    if (currentItem) {
        currentItem.style.transition = 'transform 0.3s ease-in-out, opacity 0.3s ease-in-out';
    }

    // Nur auf vertikale Swipes reagieren, wenn es sich um eine vertikale Bewegung handelt
    if (isScrolling && isDragging) {
        const SWIPE_THRESHOLD = 80; // Erhöhte Mindest-Swipe-Distanz für bessere UX
        const velocity = Math.abs(yDiff) / 100; // Einfache Geschwindigkeitsberechnung

        if (Math.abs(yDiff) > SWIPE_THRESHOLD || velocity > 0.5) {
            if (yDiff < 0) {
                // Nach oben swipen - nächstes Bild
                nextItem();
            } else {
                // Nach unten swipen - vorheriges Bild
                prevItem();
            }
        } else {
            // Kein ausreichender Swipe, zurücksetzen
            resetItemsPosition();
        }
    }

    resetTouch();
}

function resetTouch() {
    startY = null;
    startX = null;
    isScrolling = undefined;
    isDragging = false;
}

function resetItemsPosition() {
    const items = document.querySelectorAll('.swipe-item');
    items.forEach(item => {
        const itemIndex = parseInt(item.getAttribute('data-index') || '0');

        // Setze die Transition für eine sanfte Animation
        item.style.transition = 'transform 0.3s ease-in-out, opacity 0.3s ease-in-out';

        if (item.classList.contains('active')) {
            // Aktuelles Item zurücksetzen
            item.style.transform = 'translateY(0)';
            item.style.opacity = '1';
        } else {
            // Andere Items entsprechend positionieren
            const isPrev = itemIndex < currentIndex;
            item.style.transform = isPrev ? 'translateY(-100%)' : 'translateY(100%)';
            item.style.opacity = '0';

            // Verstecke nicht-aktive Items nach der Animation
            setTimeout(() => {
                if (!item.classList.contains('active')) {
                    item.style.display = 'none';
                }
            }, 300);
        }
    });
}

function getNextItem() {
    const container = document.getElementById('swipeContainer');
    if (!container) return null;

    const totalItems = parseInt(container.getAttribute('data-total') || '0');
    const nextIndex = currentIndex + 1;

    if (nextIndex >= totalItems) {
        return null; // Am Ende der Liste
    }

    return document.querySelector(`.swipe-item[data-index="${nextIndex}"]`);
}

function getPrevItem() {
    const prevIndex = currentIndex - 1;

    if (prevIndex < 0) {
        return null; // Am Anfang der Liste
    }

    return document.querySelector(`.swipe-item[data-index="${prevIndex}"]`);
}

// Video container setup
function setupVideoContainers() {
    console.log('setupVideoContainers called for active item');
    const activeItem = document.querySelector('.swipe-item.active');
    if (!activeItem) {
        console.warn('No active item found in setupVideoContainers');
        return;
    }

    const videoElement = activeItem.querySelector('video.video-content');
    const playButton = activeItem.querySelector('.play-button'); // Assuming play button is within the active item

    if (videoElement) {
        console.log('Video element found:', videoElement);
        videoElement.pause();
        videoElement.currentTime = 0;

        // Attempt to play the video (it's muted by default from createMediaElement)
        // This provides an "autoplay when visible" feel.
        const playPromise = videoElement.play();
        if (playPromise !== undefined) {
            playPromise.then(_ => {
                // Autoplay started
                if (playButton) playButton.style.display = 'none';
                console.log('Video autoplay started for:', videoElement.src);
            }).catch(error => {
                // Autoplay was prevented.
                // This can happen if the browser restricts autoplay, e.g., if not muted or no user interaction.
                // Since we explicitly mute it, this is less likely for that reason.
                if (playButton) playButton.style.display = 'block'; // Show play button if autoplay fails
                console.warn('Video autoplay prevented for:', videoElement.src, error);
            });
        } else {
            // Fallback for browsers that don't return a promise from play()
            // (Older browsers - less common now)
            if (videoElement.paused) {
                if (playButton) playButton.style.display = 'block';
            } else {
                if (playButton) playButton.style.display = 'none';
            }
        }

        // Ensure play button visibility is correct based on current state
        // This is a bit redundant with the createMediaElement listeners but acts as a safeguard
        if (playButton) {
            if (videoElement.paused) {
                playButton.style.display = 'block';
            } else {
                playButton.style.display = 'none';
            }
        }
    } else {
        console.log('No video element found in active item.');
    }
}

// Make all necessary functions globally available
window.showItem = showItem;
window.nextItem = nextItem;
window.prevItem = prevItem;
window.initSwipeListeners = initSwipeListeners;
window.setupVideoContainers = setupVideoContainers;

console.log('Navigation module initialized and functions exposed');

// Initialisiere Swipe Listener, wenn das DOM geladen ist
// document.addEventListener('DOMContentLoaded', initSwipeListeners);
// Dies wird nun von mobile.html nach dem Laden aller Skripte aufgerufen.
