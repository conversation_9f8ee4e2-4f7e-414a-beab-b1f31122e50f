/**
 * Vereinfachte Mobile Gallery für bessere Performance
 */

// Globale Variablen
window.totalLoaded = 0;
window.totalItems = 0;
window.isLoading = false;
window.currentIndex = 0;

// Vereinfachte Media-Laden-Funktion
async function loadMoreMediaSimple(limit = 3) {
    console.log(`🔄 loadMoreMediaSimple called with limit: ${limit}`);
    
    if (window.isLoading) {
        console.log('⏳ Already loading...');
        return;
    }

    window.isLoading = true;
    
    try {
        const offset = window.totalLoaded || 0;
        const url = `/api/media/batch?offset=${offset}&limit=${limit}&fast=true`;
        console.log(`🌐 Fetching: ${url}`);

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const result = await response.json();
        console.log('📦 API Response:', result);

        if (!result.success || !result.data || !result.data.items) {
            throw new Error('Invalid API response');
        }

        const container = document.querySelector('.swipe-container');
        if (!container) {
            throw new Error('Container not found');
        }

        const items = result.data.items;
        console.log(`📸 Processing ${items.length} items`);

        // Erstelle Media-Elemente
        items.forEach((item, index) => {
            const mediaElement = createSimpleMediaElement(item, window.totalLoaded + index);
            if (mediaElement) {
                container.appendChild(mediaElement);
            }
        });

        // Update counters
        window.totalLoaded += items.length;
        window.totalItems = result.data.total;

        console.log(`📊 Updated: totalLoaded=${window.totalLoaded}, totalItems=${window.totalItems}`);

        // Zeige erstes Element
        if (window.totalLoaded === items.length && items.length > 0) {
            showSimpleItem(0);
        }

        // Verstecke Loading-Indikator
        const loadingIndicator = document.querySelector('.loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }

    } catch (error) {
        console.error('❌ Error loading media:', error);
    } finally {
        window.isLoading = false;
    }
}

// Vereinfachte Media-Element-Erstellung
function createSimpleMediaElement(item, index) {
    console.log(`🎨 Creating simple element ${index}:`, item);

    const element = document.createElement('div');
    element.className = 'swipe-item';
    element.dataset.index = index;
    element.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        display: none;
        align-items: center;
        justify-content: center;
        background: #000;
    `;

    // Media-Container
    const mediaContainer = document.createElement('div');
    mediaContainer.style.cssText = `
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    `;

    // Loading-Placeholder
    const placeholder = document.createElement('div');
    placeholder.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 48px;
        z-index: 10;
    `;
    placeholder.textContent = '📷';

    // Media-Element (Bild oder Video)
    const mediaEl = document.createElement(item.type === 'video' ? 'video' : 'img');
    mediaEl.style.cssText = `
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    `;

    if (item.type === 'video') {
        mediaEl.controls = true;
        mediaEl.muted = true;
        mediaEl.preload = 'metadata';
    }

    // Bestimme Bild-URL
    let imageUrl;
    if (item.thumbnail_path) {
        imageUrl = `/media/${item.thumbnail_path}`;
    } else {
        imageUrl = `/media/${item.path}`;
    }

    console.log(`🔗 Loading: ${imageUrl}`);

    // Lade Bild/Video
    if (item.type === 'video') {
        mediaEl.src = `/media/${item.path}`;
        mediaEl.onloadedmetadata = () => {
            console.log(`✅ Video loaded: ${item.path}`);
            placeholder.style.display = 'none';
        };
        mediaEl.onerror = () => {
            console.error(`❌ Video failed: ${item.path}`);
            placeholder.textContent = '❌';
        };
    } else {
        mediaEl.src = imageUrl;
        mediaEl.onload = () => {
            console.log(`✅ Image loaded: ${imageUrl}`);
            placeholder.style.display = 'none';
            
            // Lade Vollauflösung falls Thumbnail
            if (item.thumbnail_path && imageUrl.includes('thumbnail')) {
                const fullUrl = `/media/${item.path}`;
                const fullImg = new Image();
                fullImg.onload = () => {
                    mediaEl.src = fullUrl;
                    console.log(`✅ Full image loaded: ${fullUrl}`);
                };
                fullImg.src = fullUrl;
            }
        };
        mediaEl.onerror = () => {
            console.error(`❌ Image failed: ${imageUrl}`);
            // Fallback zu Vollbild
            if (item.thumbnail_path && imageUrl.includes('thumbnail')) {
                const fullUrl = `/media/${item.path}`;
                mediaEl.src = fullUrl;
                mediaEl.onerror = () => {
                    placeholder.textContent = '❌';
                };
            } else {
                placeholder.textContent = '❌';
            }
        };
    }

    // Creator-Info
    const info = document.createElement('div');
    info.style.cssText = `
        position: absolute;
        bottom: 20px;
        left: 20px;
        color: white;
        background: rgba(0,0,0,0.5);
        padding: 10px;
        border-radius: 8px;
        z-index: 20;
    `;
    info.innerHTML = `
        <div style="font-weight: bold;">${item.creator_name}</div>
        <div style="font-size: 12px; opacity: 0.8;">${index + 1} / ${window.totalItems || '?'}</div>
    `;

    mediaContainer.appendChild(placeholder);
    mediaContainer.appendChild(mediaEl);
    mediaContainer.appendChild(info);
    element.appendChild(mediaContainer);

    return element;
}

// Vereinfachte Item-Anzeige
function showSimpleItem(index) {
    console.log(`👁️ Showing item ${index}`);
    
    const items = document.querySelectorAll('.swipe-item');
    items.forEach((item, i) => {
        item.style.display = i === index ? 'flex' : 'none';
    });
    
    window.currentIndex = index;
    
    // Lade mehr wenn nötig
    if (index >= items.length - 2 && window.totalLoaded < window.totalItems && !window.isLoading) {
        console.log('🔄 Loading more items...');
        loadMoreMediaSimple(2);
    }
}

// Touch-Events für Swipe
function initSimpleSwipe() {
    const container = document.querySelector('.swipe-container');
    if (!container) return;

    let startY = 0;
    let startTime = 0;

    container.addEventListener('touchstart', (e) => {
        startY = e.touches[0].clientY;
        startTime = Date.now();
    });

    container.addEventListener('touchend', (e) => {
        const endY = e.changedTouches[0].clientY;
        const endTime = Date.now();
        const deltaY = endY - startY;
        const deltaTime = endTime - startTime;

        // Swipe-Erkennung
        if (Math.abs(deltaY) > 50 && deltaTime < 500) {
            if (deltaY > 0 && window.currentIndex > 0) {
                // Swipe nach unten - vorheriges Bild
                showSimpleItem(window.currentIndex - 1);
            } else if (deltaY < 0 && window.currentIndex < window.totalLoaded - 1) {
                // Swipe nach oben - nächstes Bild
                showSimpleItem(window.currentIndex + 1);
            }
        }
    });
}

// Globale Funktionen verfügbar machen
window.loadMoreMedia = loadMoreMediaSimple;
window.showItem = showSimpleItem;
window.createMediaElement = createSimpleMediaElement;

// Auto-Initialisierung
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Simple Mobile Gallery initialized');
    initSimpleSwipe();
    loadMoreMediaSimple(3);
});

console.log('📱 Simple Mobile Gallery module loaded');
