{% extends "base.html" %}

{% block title %}CiitAI-Galerie - Mobile-Ansicht{% endblock %}

{% block extra_css %}
<!-- Kritische CSS direkt einbinden -->
<style>
    /* Minimales Styling für schnellen First Paint */
    body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; }
    .swipe-container { height: 100vh; position: relative; }
    .swipe-item { position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: none; }
    .swipe-item.active { display: flex; }
    .media-container { flex: 1; display: flex; align-items: center; justify-content: center; }
    .media-container img, .media-container video { max-width: 100%; max-height: 100%; object-fit: contain; }
    .loading { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: #000; display: flex; align-items: center; justify-content: center; z-index: 1000; }
    .spinner { width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
</style>
<!-- Restliches CSS asynchron nachladen -->
<link rel="preload" href="{{ url_for('static', filename='css/mobile.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="{{ url_for('static', filename='css/mobile.css') }}"></noscript>
{% endblock %}

{% block content %}
<!-- TikTok-style Mobile Gallery Container -->
<div class="swipe-container" id="swipeContainer" data-total="{{ media_batch.data.total if media_batch and media_batch.data else 0 }}">
    <!-- Loading indicator for initial load -->
    <div class="loading-indicator" id="loadingIndicator" style="display: flex;">
        <div class="spinner"></div>
    </div>

    <!-- Progress indicator -->
    <div class="progress-indicator" id="progressIndicator" style="display: none;">
        <!-- Progress dots will be added dynamically -->
    </div>
</div>

<!-- Global loading overlay -->
<div id="loadingOverlay" class="loading" style="display: flex;">
    <div class="spinner"></div>
</div>

<!-- Scripts werden über das extra_js Block geladen -->
{% endblock %}

{% block extra_js %}
<!-- Lade JavaScript-Dateien synchron für bessere Kontrolle -->
<script src="{{ url_for('static', filename='js/mobile-gallery-core.js') }}"></script>
<script src="{{ url_for('static', filename='js/mobile-gallery-media.js') }}"></script>
<script src="{{ url_for('static', filename='js/mobile-gallery-navigation.js') }}"></script>

<script>
// Vereinfachte Initialisierung
(function() {
    console.log('🚀 Mobile Gallery - Vereinfachte Initialisierung');

    // Lade-Overlay ausblenden
    setTimeout(function() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
            console.log('✅ Loading overlay hidden');
        }
    }, 100);

    // Einfache Initialisierung
    function initializeGallery() {
        console.log('📱 Starte Galerie-Initialisierung...');

        // Initialisiere globale Variablen
        window.totalLoaded = 0;
        window.totalItems = 0;
        window.isLoading = false;
        window.preventAutoLoad = false;

        console.log('📊 Globale Variablen initialisiert');

        // Lade erste Medien
        if (typeof window.loadMoreMedia === 'function') {
            console.log('📞 Rufe loadMoreMedia auf...');
            window.loadMoreMedia(3);
        } else {
            console.error('❌ loadMoreMedia Funktion nicht gefunden!');
        }
    }

    // Starte die Initialisierung
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeGallery);
    } else {
        initializeGallery();
    }
})();

// Debug-Ausgabe für verfügbare Funktionen
console.log('🔍 Verfügbare Funktionen:');
console.log('- loadMoreMedia:', typeof window.loadMoreMedia);
console.log('- createMediaElement:', typeof window.createMediaElement);
console.log('- showItem:', typeof window.showItem);
</script>
{% endblock %}
