{% extends "base.html" %}

{% block title %}CiitAI-Galerie - Mobile-Ansicht{% endblock %}

{% block extra_css %}
<!-- Kritische CSS direkt einbinden -->
<style>
    /* Minimales Styling für schnellen First Paint */
    body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; }
    .swipe-container { height: 100vh; position: relative; }
    .swipe-item { position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: none; }
    .swipe-item.active { display: flex; }
    .media-container { flex: 1; display: flex; align-items: center; justify-content: center; }
    .media-container img, .media-container video { max-width: 100%; max-height: 100%; object-fit: contain; }
    .loading { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: #000; display: flex; align-items: center; justify-content: center; z-index: 1000; }
    .spinner { width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
</style>
<!-- Restliches CSS asynchron nachladen -->
<link rel="preload" href="{{ url_for('static', filename='css/mobile.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="{{ url_for('static', filename='css/mobile.css') }}"></noscript>
{% endblock %}

{% block content %}
<!-- TikTok-style Mobile Gallery Container -->
<div class="swipe-container" id="swipeContainer" data-total="{{ media_batch.data.total if media_batch and media_batch.data else 0 }}">
    <!-- Loading indicator for initial load -->
    <div class="loading-indicator" id="loadingIndicator" style="display: flex;">
        <div class="spinner"></div>
    </div>

    <!-- Progress indicator -->
    <div class="progress-indicator" id="progressIndicator" style="display: none;">
        <!-- Progress dots will be added dynamically -->
    </div>
</div>

<!-- Global loading overlay -->
<div id="loadingOverlay" class="loading" style="display: flex;">
    <div class="spinner"></div>
</div>

<!-- Scripts for TikTok-style mobile gallery -->
<script src="{{ url_for('static', filename='js/mobile-gallery-core.js') }}"></script>
<script src="{{ url_for('static', filename='js/mobile-gallery-media.js') }}"></script>
<script src="{{ url_for('static', filename='js/mobile-gallery-navigation.js') }}"></script>
<script src="{{ url_for('static', filename='js/mobile-gallery-video.js') }}" async></script>
{% endblock %}

{% block extra_js %}
<script>
// Hauptinitialisierung wenn das DOM geladen ist
(function() {
    // Prevent multiple initializations
    if (window.galleryInitialized) {
        console.warn('Gallery already initialized, skipping...');
        return;
    }

    // Mark as initialized immediately
    window.galleryInitialized = true;

    console.log('DOMContentLoaded - Starting gallery initialization...');

    // Lade-Overlay ausblenden
    setTimeout(function() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
            console.log('Loading overlay hidden');
        }
    }, 500);

    // Hilfsfunktion zum Laden von Skripten
    function loadScript(src, callback) {
        console.log('Loading script:', src);
        return new Promise((resolve, reject) => {
            // Prüfen, ob das Skript bereits geladen wurde
            const existingScript = document.querySelector(`script[src="${src}"]`);
            if (existingScript) {
                console.log('Script already loaded, skipping:', src);
                if (callback) callback();
                return resolve();
            }

            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            script.onload = () => {
                console.log('Script loaded successfully:', src);
                if (callback) callback();
                resolve();
            };
            script.onerror = (error) => {
                console.error('Error loading script:', src, error);
                reject(error);
            };
            document.body.appendChild(script);
        });
    }

    // Initialisierung der Galerie
    async function initializeGallery() {
        console.log('Starting gallery initialization...');

        try {
            // Lade die benötigten Skripte nacheinander in der richtigen Reihenfolge
            console.log('Loading core script...');
            await loadScript("{{ url_for('static', filename='js/mobile-gallery-core.js') }}");

            console.log('Loading media script...');
            await loadScript("{{ url_for('static', filename='js/mobile-gallery-media.js') }}");

            console.log('Loading navigation script...');
            await loadScript("{{ url_for('static', filename='js/mobile-gallery-navigation.js') }}");

            // Initialisiere die Galerie, wenn die Funktion verfügbar ist
            if (typeof window.initGallery === 'function') {
                console.log('Initializing gallery...');
                window.initGallery();
            } else {
                console.warn('initGallery function not found');
            }

            // Initialisiere Swipe-Listener, falls verfügbar
            if (typeof window.initSwipeListeners === 'function') {
                console.log('Initializing swipe listeners...');
                window.initSwipeListeners();
            } else {
                console.warn('initSwipeListeners function not found');
            }

            console.log('Gallery initialization complete');

            // Zeige das erste Element an, falls noch nicht geschehen
            const swipeItems = document.querySelectorAll('.swipe-item');
            if (swipeItems.length > 0 && typeof window.showItem === 'function') {
                window.showItem(0);

                // Lade erstes Bild in voller Auflösung
                const firstLazyImage = swipeItems[0].querySelector('.lazy-load-image');
                if (firstLazyImage && typeof window.loadFullImage === 'function') {
                    window.loadFullImage(firstLazyImage);
                }
            }

            // Lade mehr Inhalte, falls nötig
            if (window.totalLoaded < 5 && window.totalLoaded < window.totalItems && typeof window.loadMoreMedia === 'function') {
                console.log('Loading initial batch of media...');
                window.loadMoreMedia();
            }

        } catch (error) {
            console.error('Fehler beim Initialisieren der Galerie:', error);
        }
    }

    // Starte die Initialisierung wenn das DOM geladen ist
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeGallery);
    } else {
        initializeGallery().catch(error => {
            console.error('Error during gallery initialization:', error);
        });
    }
})();

// Swipe-Event-Handler
function initSwipeListeners() {
    const container = document.querySelector('.swipe-container');
    if (!container) return;

    let startY = null;
    let startX = null;
    let isScrolling = null;

    // Touch-Start Event
    container.addEventListener('touchstart', (e) => {
        const touch = e.touches[0];
        startY = touch.clientY;
        startX = touch.clientX;
        isScrolling = undefined;
    }, { passive: true });

    // Touch-Move Event
    container.addEventListener('touchmove', (e) => {
        if (startY === null || startX === null) return;

        const touch = e.touches[0];
        const yDiff = touch.clientY - startY;
        const xDiff = touch.clientX - startX;

        // Bestimme die Hauptbewegungsrichtung beim ersten Move-Event
        if (isScrolling === undefined) {
            isScrolling = Math.abs(yDiff) > Math.abs(xDiff);
        }

        // Verhindere Scrollen, wenn wir swipen
        if (!isScrolling) {
            e.preventDefault();
        }
    }, { passive: false });

    // Touch-End Event
    container.addEventListener('touchend', (e) => {
        if (startY === null || startX === null || isScrolling) {
            resetTouch();
            return;
        }

        const touch = e.changedTouches[0];
        const yDiff = touch.clientY - startY;

        // Nur vertikale Swipes verarbeiten (Mindestdistanz: 50px)
        if (Math.abs(yDiff) > 50) {
            if (yDiff > 0 && typeof showPrevious === 'function') {
                showPrevious();
            } else if (typeof showNext === 'function') {
                showNext();
            }
        }

        resetTouch();
    }, { passive: true });

    // Setzt die Touch-Variablen zurück
    function resetTouch() {
        startY = null;
        startX = null;
        isScrolling = null;
    }
}

// Globale Funktionen verfügbar machen
if (typeof showItem !== 'undefined') window.showItem = showItem;
if (typeof loadFullImage !== 'undefined') window.loadFullImage = loadFullImage;
if (typeof toggleMetadata !== 'undefined') window.toggleMetadata = toggleMetadata;
if (typeof loadMoreMedia !== 'undefined') window.loadMoreMedia = loadMoreMedia;
</script>
{% endblock %}
