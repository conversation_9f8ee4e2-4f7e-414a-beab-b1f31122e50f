<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CiitAI-Galerie - Mobile</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body, html {
            height: 100%;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }
        
        .swipe-container {
            position: relative;
            width: 100%;
            height: 100vh;
            background: #000;
        }
        
        .swipe-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .swipe-item.active {
            display: flex;
        }
        
        .loading-indicator {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            color: white;
            font-size: 24px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #333;
            border-top: 4px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Touch-optimiert */
        .swipe-container {
            touch-action: pan-y;
            -webkit-overflow-scrolling: touch;
        }
        
        /* Responsive Bilder */
        img, video {
            max-width: 100%;
            max-height: 100vh;
            object-fit: contain;
            user-select: none;
            -webkit-user-select: none;
        }
        
        /* Debug-Info */
        .debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 2000;
            display: none;
        }
        
        /* Navigation Hints */
        .nav-hint {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255,255,255,0.5);
            font-size: 14px;
            z-index: 100;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Main Container -->
    <div class="swipe-container" id="swipeContainer">
        <!-- Loading Indicator -->
        <div class="loading-indicator" id="loadingIndicator">
            <div class="spinner"></div>
            <span>Lade Bilder...</span>
        </div>
        
        <!-- Navigation Hint -->
        <div class="nav-hint">
            ↑ Swipe<br>
            ↓ Swipe
        </div>
    </div>

    <!-- Debug Info -->
    <div class="debug-info" id="debugInfo">
        <div>Geladen: <span id="loadedCount">0</span></div>
        <div>Gesamt: <span id="totalCount">0</span></div>
        <div>Aktuell: <span id="currentIndex">0</span></div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/mobile-gallery-simple.js') }}"></script>
    
    <script>
        // Debug-Modus aktivieren (optional)
        const DEBUG = true;
        
        if (DEBUG) {
            document.getElementById('debugInfo').style.display = 'block';
            
            // Update Debug-Info
            setInterval(() => {
                document.getElementById('loadedCount').textContent = window.totalLoaded || 0;
                document.getElementById('totalCount').textContent = window.totalItems || 0;
                document.getElementById('currentIndex').textContent = window.currentIndex || 0;
            }, 1000);
            
            // Keyboard-Navigation für Desktop-Testing
            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowUp' && window.currentIndex > 0) {
                    window.showItem(window.currentIndex - 1);
                } else if (e.key === 'ArrowDown' && window.currentIndex < window.totalLoaded - 1) {
                    window.showItem(window.currentIndex + 1);
                } else if (e.key === 'r') {
                    location.reload();
                }
            });
        }
        
        // Performance-Monitoring
        window.addEventListener('load', () => {
            console.log('🎯 Page loaded in:', performance.now().toFixed(2), 'ms');
        });
        
        // Error-Handling
        window.addEventListener('error', (e) => {
            console.error('🚨 Global error:', e.error);
        });
        
        // Unhandled Promise Rejections
        window.addEventListener('unhandledrejection', (e) => {
            console.error('🚨 Unhandled promise rejection:', e.reason);
        });
        
        console.log('📱 Mobile Simple Gallery page loaded');
    </script>
</body>
</html>
